"use client"

import { useState, useEffect } from "react"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Line,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "@/components/ui/chart"
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar, TrendingUp, Weight, Clock } from "lucide-react"

export default function ProgressPage() {
  const [workoutData, setWorkoutData] = useState([])
  const [weightData, setWeightData] = useState([])

  // Simulate loading data from localStorage on component mount
  useEffect(() => {
    // In a real app, this would come from localStorage or an API
    const mockWorkoutData = [
      { date: "01/03", duration: 45, calories: 320, type: "Musculation" },
      { date: "03/03", duration: 30, calories: 250, type: "HIIT" },
      { date: "05/03", duration: 60, calories: 450, type: "Cardio" },
      { date: "08/03", duration: 40, calories: 300, type: "Musculation" },
      { date: "10/03", duration: 35, calories: 280, type: "HIIT" },
      { date: "12/03", duration: 50, calories: 380, type: "Cardio" },
      { date: "15/03", duration: 55, calories: 420, type: "Musculation" },
    ]

    const mockWeightData = [
      { date: "01/03", weight: 75.5 },
      { date: "08/03", weight: 74.8 },
      { date: "15/03", weight: 74.2 },
    ]

    setWorkoutData(mockWorkoutData)
    setWeightData(mockWeightData)
  }, [])

  // Calculate stats
  const totalWorkouts = workoutData.length
  const totalDuration = workoutData.reduce((sum, workout) => sum + workout.duration, 0)
  const totalCalories = workoutData.reduce((sum, workout) => sum + workout.calories, 0)

  // Calculate weight change
  const weightChange =
    weightData.length >= 2 ? (weightData[weightData.length - 1].weight - weightData[0].weight).toFixed(1) : 0

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-4">Suivi de progression</h1>
        <p className="text-gray-600 text-center max-w-2xl mx-auto mb-12">
          Visualisez votre progression et analysez vos performances pour atteindre vos objectifs fitness.
        </p>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <StatCard
            icon={<Calendar className="h-8 w-8 text-blue-600" />}
            title="Entraînements"
            value={totalWorkouts}
            description="Total des séances"
          />
          <StatCard
            icon={<Clock className="h-8 w-8 text-blue-600" />}
            title="Minutes"
            value={totalDuration}
            description="Temps d'entraînement"
          />
          <StatCard
            icon={<TrendingUp className="h-8 w-8 text-blue-600" />}
            title="Calories"
            value={totalCalories}
            description="Calories brûlées"
          />
          <StatCard
            icon={<Weight className="h-8 w-8 text-blue-600" />}
            title="Poids"
            value={`${weightChange} kg`}
            description={weightChange < 0 ? "Perte de poids" : "Gain de poids"}
            trend={weightChange < 0 ? "down" : "up"}
          />
        </div>

        {/* Charts */}
        <Tabs defaultValue="workouts" className="mb-12">
          <TabsList className="mb-6">
            <TabsTrigger value="workouts">Entraînements</TabsTrigger>
            <TabsTrigger value="weight">Poids</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
          </TabsList>

          <TabsContent value="workouts">
            <Card>
              <CardHeader>
                <CardTitle>Historique des entraînements</CardTitle>
                <CardDescription>Durée et calories brûlées par séance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={workoutData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis yAxisId="left" orientation="left" stroke="#2563EB" />
                      <YAxis yAxisId="right" orientation="right" stroke="#F97316" />
                      <Tooltip />
                      <Legend />
                      <Bar yAxisId="left" dataKey="duration" name="Durée (min)" fill="#2563EB" />
                      <Bar yAxisId="right" dataKey="calories" name="Calories" fill="#F97316" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="weight">
            <Card>
              <CardHeader>
                <CardTitle>Suivi du poids</CardTitle>
                <CardDescription>Évolution de votre poids au fil du temps</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={weightData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis domain={["dataMin - 1", "dataMax + 1"]} />
                      <Tooltip />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="weight"
                        name="Poids (kg)"
                        stroke="#2563EB"
                        strokeWidth={2}
                        dot={{ r: 6 }}
                        activeDot={{ r: 8 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance">
            <Card>
              <CardHeader>
                <CardTitle>Performance par type d'entraînement</CardTitle>
                <CardDescription>Répartition de vos séances par catégorie</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        { name: "Musculation", sessions: workoutData.filter((w) => w.type === "Musculation").length },
                        { name: "Cardio", sessions: workoutData.filter((w) => w.type === "Cardio").length },
                        { name: "HIIT", sessions: workoutData.filter((w) => w.type === "HIIT").length },
                        { name: "Mobilité", sessions: workoutData.filter((w) => w.type === "Mobilité").length || 0 },
                      ]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="sessions" name="Nombre de séances" fill="#2563EB" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Add Workout Form */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold mb-6">Ajouter un entraînement</h2>
          <form className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
              <input
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Type d'entraînement</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="musculation">Musculation</option>
                <option value="cardio">Cardio</option>
                <option value="hiit">HIIT</option>
                <option value="mobilite">Mobilité</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Durée (minutes)</label>
              <input
                type="number"
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Calories brûlées</label>
              <input
                type="number"
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="md:col-span-2 lg:col-span-4 flex justify-end">
              <button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md transition-colors"
              >
                Enregistrer l'entraînement
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

function StatCard({ icon, title, value, description, trend }) {
  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center mb-4">
        <div className="mr-3">{icon}</div>
        <h3 className="font-semibold text-gray-600">{title}</h3>
      </div>
      <div className="flex items-end">
        <div className="text-3xl font-bold mr-2">{value}</div>
        {trend && (
          <div className={`text-sm font-medium ${trend === "down" ? "text-green-500" : "text-red-500"}`}>
            {trend === "down" ? "▼" : "▲"}
          </div>
        )}
      </div>
      <div className="text-sm text-gray-500 mt-1">{description}</div>
    </div>
  )
}

