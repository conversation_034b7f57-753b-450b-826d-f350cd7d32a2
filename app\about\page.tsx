import Link from "next/link"
import { ArrowR<PERSON>, CheckCircle, Users, Clock, Shield } from "lucide-react"

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-4">Pourquoi choisir FitTracker</h1>
        <p className="text-gray-600 text-center max-w-2xl mx-auto mb-12">
          Découvrez pourquoi FitTracker est l'application idéale pour vous accompagner dans votre parcours fitness.
        </p>

        {/* Main Benefits */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="h-48 bg-gradient-to-r from-blue-600 to-blue-800 flex items-center justify-center">
              <Users className="h-20 w-20 text-white" />
            </div>
            <div className="p-8">
              <h2 className="text-2xl font-bold mb-4">Conçu pour tous les niveaux</h2>
              <p className="text-gray-600 mb-6">
                Que vous soyez débutant ou athlète confirmé, FitTracker s'adapte à votre niveau et vous propose des
                programmes adaptés à vos besoins.
              </p>
              <ul className="space-y-3">
                <BenefitItem text="Programmes pour débutants, intermédiaires et avancés" />
                <BenefitItem text="Interface intuitive et facile à utiliser" />
                <BenefitItem text="Progression adaptée à votre rythme" />
              </ul>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg overflow-hidden">
            <div className="h-48 bg-gradient-to-r from-orange-500 to-orange-600 flex items-center justify-center">
              <Clock className="h-20 w-20 text-white" />
            </div>
            <div className="p-8">
              <h2 className="text-2xl font-bold mb-4">Optimisez votre temps</h2>
              <p className="text-gray-600 mb-6">
                FitTracker vous aide à maximiser l'efficacité de vos entraînements grâce à des outils précis et des
                programmes structurés.
              </p>
              <ul className="space-y-3">
                <BenefitItem text="Minuteur précis pour les entraînements par intervalles" />
                <BenefitItem text="Programmes HIIT pour des résultats rapides" />
                <BenefitItem text="Suivi en temps réel de vos performances" />
              </ul>
            </div>
          </div>
        </div>

        {/* Privacy Section */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-16">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/3 mb-6 md:mb-0 flex justify-center">
              <Shield className="h-32 w-32 text-blue-600" />
            </div>
            <div className="md:w-2/3 md:pl-8">
              <h2 className="text-2xl font-bold mb-4">Votre vie privée est notre priorité</h2>
              <p className="text-gray-600 mb-6">
                Contrairement à de nombreuses applications fitness, FitTracker respecte totalement votre vie privée.
                Toutes vos données restent sur votre appareil.
              </p>
              <ul className="space-y-3">
                <BenefitItem text="Aucune donnée personnelle envoyée à des serveurs externes" />
                <BenefitItem text="Fonctionne entièrement hors ligne" />
                <BenefitItem text="Pas de publicités ni de trackers" />
              </ul>
            </div>
          </div>
        </div>

        {/* Testimonials */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-center mb-8">Ce que nos utilisateurs disent</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold mr-4">
                  M
                </div>
                <div>
                  <p className="font-bold">Marc D.</p>
                  <p className="text-sm text-gray-500">Utilisateur depuis 8 mois</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "J'ai essayé plusieurs applications de fitness, mais FitTracker est de loin la plus simple à utiliser.
                Le minuteur d'entraînement est parfait pour mes séances HIIT."
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold mr-4">
                  L
                </div>
                <div>
                  <p className="font-bold">Laura S.</p>
                  <p className="text-sm text-gray-500">Utilisatrice depuis 3 mois</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "Les graphiques de progression sont très motivants. Je peux voir mes améliorations semaine après
                semaine, ce qui me pousse à continuer."
              </p>
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold mr-4">
                  T
                </div>
                <div>
                  <p className="font-bold">Thomas B.</p>
                  <p className="text-sm text-gray-500">Coach sportif</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "Je recommande FitTracker à tous mes clients. L'application est complète, intuitive et ne nécessite pas
                d'abonnement, ce qui est rare de nos jours."
              </p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 rounded-xl shadow-lg p-8 text-white text-center">
          <h2 className="text-3xl font-bold mb-4">Prêt à transformer votre routine fitness ?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Commencez dès aujourd'hui à suivre vos entraînements et à visualiser votre progression avec FitTracker.
          </p>
          <Link
            href="/timer"
            className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-8 rounded-lg inline-flex items-center transition-colors"
          >
            Commencer maintenant <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </div>
  )
}

function BenefitItem({ text }) {
  return (
    <li className="flex items-start">
      <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
      <span className="text-gray-700">{text}</span>
    </li>
  )
}

