import { Clock, BarChart3, <PERSON><PERSON><PERSON>, Smartphone, CloudOff, Lock, Zap, Award } from "lucide-react"

export default function FeaturesPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-4">Fonctionnalités de FitTracker</h1>
        <p className="text-gray-600 text-center max-w-2xl mx-auto mb-12">
          Découvrez toutes les fonctionnalités qui font de FitTracker l'application idéale pour suivre votre progression
          fitness.
        </p>

        {/* Main Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          <FeatureCard
            icon={<Clock className="h-12 w-12 text-orange-500" />}
            title="Minuteur d'entraînement avancé"
            description="Configurez des intervalles personnalisés pour vos entraînements HIIT, <PERSON><PERSON><PERSON>, EMOM ou utilisez nos préréglages. Le minuteur vous guide avec des alertes visuelles et sonores pour maximiser vos séances."
          />

          <FeatureCard
            icon={<Dumbbell className="h-12 w-12 text-orange-500" />}
            title="Programmes d'entraînement catégorisés"
            description="Accédez à une bibliothèque de programmes d'entraînement pour tous les niveaux, de débutant à avancé, couvrant la musculation, le cardio, le HIIT et la mobilité."
          />

          <FeatureCard
            icon={<BarChart3 className="h-12 w-12 text-orange-500" />}
            title="Suivi de progression détaillé"
            description="Visualisez votre progression avec des graphiques intuitifs montrant l'évolution de vos performances, de votre poids et de vos statistiques d'entraînement au fil du temps."
          />

          <FeatureCard
            icon={<Smartphone className="h-12 w-12 text-orange-500" />}
            title="Design responsive"
            description="Utilisez FitTracker sur n'importe quel appareil - ordinateur, tablette ou smartphone. L'interface s'adapte parfaitement à toutes les tailles d'écran."
          />
        </div>

        {/* Additional Features */}
        <h2 className="text-2xl font-bold text-center mb-8">Avantages supplémentaires</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
          <SmallFeatureCard
            icon={<CloudOff className="h-6 w-6 text-blue-600" />}
            title="Fonctionne hors ligne"
            description="Accédez à vos données même sans connexion internet."
          />

          <SmallFeatureCard
            icon={<Lock className="h-6 w-6 text-blue-600" />}
            title="Données sécurisées"
            description="Vos informations sont stockées localement sur votre appareil."
          />

          <SmallFeatureCard
            icon={<Zap className="h-6 w-6 text-blue-600" />}
            title="Performance optimisée"
            description="Application légère et rapide, sans ralentissements."
          />

          <SmallFeatureCard
            icon={<Award className="h-6 w-6 text-blue-600" />}
            title="Badges de progression"
            description="Débloquez des badges en atteignant vos objectifs fitness."
          />
        </div>

        {/* Feature Comparison */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-16">
          <h2 className="text-2xl font-bold text-center mb-8">FitTracker vs. Autres applications</h2>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b-2 border-gray-200">
                  <th className="py-3 px-4 text-left">Fonctionnalité</th>
                  <th className="py-3 px-4 text-center">FitTracker</th>
                  <th className="py-3 px-4 text-center">Applications standard</th>
                  <th className="py-3 px-4 text-center">Applications premium</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 font-medium">Minuteur personnalisable</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                  <td className="py-3 px-4 text-center text-red-500">✗</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                </tr>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 font-medium">Programmes d'entraînement</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                </tr>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 font-medium">Graphiques de progression</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                  <td className="py-3 px-4 text-center text-red-500">✗</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                </tr>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 font-medium">Fonctionne hors ligne</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                  <td className="py-3 px-4 text-center text-red-500">✗</td>
                  <td className="py-3 px-4 text-center text-red-500">✗</td>
                </tr>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 font-medium">Sans abonnement</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                  <td className="py-3 px-4 text-center text-red-500">✗</td>
                </tr>
                <tr>
                  <td className="py-3 px-4 font-medium">Pas de publicités</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                  <td className="py-3 px-4 text-center text-red-500">✗</td>
                  <td className="py-3 px-4 text-center text-green-500">✓</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        {/* FAQ */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-center mb-8">Questions fréquentes</h2>
          <div className="space-y-6">
            <FaqItem
              question="FitTracker est-il gratuit ?"
              answer="Oui, FitTracker est entièrement gratuit. Nous ne proposons pas d'achats intégrés ni d'abonnements premium."
            />
            <FaqItem
              question="Mes données sont-elles sauvegardées ?"
              answer="Oui, toutes vos données sont sauvegardées localement sur votre appareil grâce au localStorage. Elles persistent même si vous fermez l'application."
            />
            <FaqItem
              question="Puis-je utiliser FitTracker sur mon téléphone ?"
              answer="Absolument ! FitTracker est conçu avec un design responsive qui s'adapte à tous les appareils, des ordinateurs de bureau aux smartphones."
            />
            <FaqItem
              question="Comment ajouter un nouvel entraînement ?"
              answer="Rendez-vous dans la section 'Progression', puis utilisez le formulaire 'Ajouter un entraînement' en bas de page pour enregistrer vos séances."
            />
          </div>
        </div>
      </div>
    </div>
  )
}

function FeatureCard({ icon, title, description }) {
  return (
    <div className="bg-white rounded-xl shadow-lg p-8">
      <div className="mb-6">{icon}</div>
      <h3 className="text-xl font-bold mb-4 text-blue-800">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  )
}

function SmallFeatureCard({ icon, title, description }) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="mb-4">{icon}</div>
      <h3 className="font-bold mb-2">{title}</h3>
      <p className="text-sm text-gray-600">{description}</p>
    </div>
  )
}

function FaqItem({ question, answer }) {
  return (
    <div className="border-b border-gray-200 pb-6">
      <h3 className="font-bold text-lg mb-2">{question}</h3>
      <p className="text-gray-600">{answer}</p>
    </div>
  )
}

