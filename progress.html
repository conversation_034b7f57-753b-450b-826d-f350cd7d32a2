<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Suivi de progression - FitTracker</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="index.html" class="logo">Fit<span>Tracker</span></a>
            </div>
            <div class="navbar-menu" id="navbarMenu">
                <ul class="navbar-nav">
                    <li><a href="index.html">Accueil</a></li>
                    <li><a href="timer.html">Minuteur</a></li>
                    <li><a href="programs.html">Programmes</a></li>
                    <li><a href="progress.html" class="active">Progression</a></li>
                    <li><a href="features.html">Fonctionnalités</a></li>
                    <li><a href="about.html">À propos</a></li>
                </ul>
            </div>
            <div class="navbar-toggle" id="navbarToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Progress Section -->
    <section class="progress-section">
        <div class="container">
            <h1 class="page-title">Suivi de progression</h1>
            <p class="page-description">Visualisez votre progression et analysez vos performances pour atteindre vos objectifs fitness.</p>
            
            <!-- Stats Overview -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <img src="img/icons/calendar.svg" alt="Entraînements">
                    </div>
                    <div class="stat-content">
                        <h3>Entraînements</h3>
                        <div class="stat-value" id="totalWorkouts">0</div>
                        <div class="stat-description">Total des séances</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <img src="img/icons/clock.svg" alt="Minutes">
                    </div>
                    <div class="stat-content">
                        <h3>Minutes</h3>
                        <div class="stat-value" id="totalDuration">0</div>
                        <div class="stat-description">Temps d'entraînement</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <img src="img/icons/trending-up.svg" alt="Calories">
                    </div>
                    <div class="stat-content">
                        <h3>Calories</h3>
                        <div class="stat-value" id="totalCalories">0</div>
                        <div class="stat-description">Calories brûlées</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <img src="img/icons/weight.svg" alt="Poids">
                    </div>
                    <div class="stat-content">
                        <h3>Poids</h3>
                        <div class="stat-value" id="weightChange">0 kg</div>
                        <div class="stat-description" id="weightTrend">Variation de poids</div>
                    </div>
                </div>
            </div>
            
            <!-- Charts -->
            <div class="charts-container">
                <div class="charts-tabs">
                    <button class="chart-tab active" data-tab="workouts">Entraînements</button>
                    <button class="chart-tab" data-tab="weight">Poids</button>
                    <button class="chart-tab" data-tab="performance">Performance</button>
                </div>
                
                <div class="chart-content">
                    <div class="chart-panel active" id="workoutsChart">
                        <h3>Historique des entraînements</h3>
                        <p>Durée et calories brûlées par séance</p>
                        <div class="chart-container">
                            <canvas id="workoutsCanvas"></canvas>
                        </div>
                    </div>
                    
                    <div class="chart-panel" id="weightChart">
                        <h3>Suivi du poids</h3>
                        <p>Évolution de votre poids au fil du temps</p>
                        <div class="chart-container">
                            <canvas id="weightCanvas"></canvas>
                        </div>
                    </div>
                    
                    <div class="chart-panel" id="performanceChart">
                        <h3>Performance par type d'entraînement</h3>
                        <p>Répartition de vos séances par catégorie</p>
                        <div class="chart-container">
                            <canvas id="performanceCanvas"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Add Workout Form -->
            <div class="add-workout">
                <h2>Ajouter un entraînement</h2>
                <form id="workoutForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="workoutDate">Date</label>
                            <input type="date" id="workoutDate" required>
                        </div>
                        <div class="form-group">
                            <label for="workoutType">Type d'entraînement</label>
                            <select id="workoutType" required>
                                <option value="musculation">Musculation</option>
                                <option value="cardio">Cardio</option>
                                <option value="hiit">HIIT</option>
                                <option value="mobilite">Mobilité</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="workoutDuration">Durée (minutes)</label>
                            <input type="number" id="workoutDuration" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="workoutCalories">Calories brûlées</label>
                            <input type="number" id="workoutCalories" min="1" required>
                        </div>
                    </div>
                    <div class="form-submit">
                        <button type="submit" class="btn btn-primary">Enregistrer l'entraînement</button>
                    </div>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-brand">
                    <h3 class="footer-logo">Fit<span>Tracker</span></h3>
                    <p>Suivez vos entraînements, définissez des objectifs et visualisez votre progression en un seul endroit.</p>
                </div>
                <div class="footer-links">
                    <h4>Liens rapides</h4>
                    <ul>
                        <li><a href="index.html">Accueil</a></li>
                        <li><a href="timer.html">Minuteur</a></li>
                        <li><a href="programs.html">Programmes</a></li>
                        <li><a href="progress.html">Progression</a></li>
                        <li><a href="features.html">Fonctionnalités</a></li>
                        <li><a href="about.html">À propos</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">FAQ</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">Aide</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h4>Légal</h4>
                    <ul>
                        <li><a href="#">Conditions d'utilisation</a></li>
                        <li><a href="#">Politique de confidentialité</a></li>
                        <li><a href="#">Cookies</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <span id="currentYear"></span> FitTracker. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/progress.js"></script>
</body>
</html>

