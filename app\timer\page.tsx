"use client"

import { useState, useEffect, useRef } from "react"
import { Play, Pause, RotateCcw, Settings } from "lucide-react"

export default function TimerPage() {
  const [timeLeft, setTimeLeft] = useState(0)
  const [isActive, setIsActive] = useState(false)
  const [workTime, setWorkTime] = useState(25 * 60) // 25 minutes in seconds
  const [restTime, setRestTime] = useState(5 * 60) // 5 minutes in seconds
  const [isWorkPhase, setIsWorkPhase] = useState(true)
  const [rounds, setRounds] = useState(1)
  const [currentRound, setCurrentRound] = useState(1)
  const [showSettings, setShowSettings] = useState(false)

  const intervalRef = useRef(null)

  // Initialize timer
  useEffect(() => {
    setTimeLeft(workTime)
  }, [workTime])

  // Timer logic
  useEffect(() => {
    if (isActive && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft((prevTime) => prevTime - 1)
      }, 1000)
    } else if (isActive && timeLeft === 0) {
      // Phase completed
      if (isWorkPhase) {
        // Work phase completed, switch to rest
        setIsWorkPhase(false)
        setTimeLeft(restTime)
      } else {
        // Rest phase completed
        if (currentRound < rounds) {
          // Move to next round
          setCurrentRound((prevRound) => prevRound + 1)
          setIsWorkPhase(true)
          setTimeLeft(workTime)
        } else {
          // Workout completed
          resetTimer()
          // Play sound or notification here
          alert("Entraînement terminé !")
        }
      }
    }

    return () => {
      if (intervalRef.current) clearInterval(intervalRef.current)
    }
  }, [isActive, timeLeft, isWorkPhase, currentRound, rounds, workTime, restTime])

  const toggleTimer = () => {
    setIsActive(!isActive)
  }

  const resetTimer = () => {
    setIsActive(false)
    setIsWorkPhase(true)
    setTimeLeft(workTime)
    setCurrentRound(1)
    if (intervalRef.current) clearInterval(intervalRef.current)
  }

  const toggleSettings = () => {
    setShowSettings(!showSettings)
    if (isActive) {
      setIsActive(false)
      if (intervalRef.current) clearInterval(intervalRef.current)
    }
  }

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`
  }

  const handleWorkTimeChange = (e) => {
    const value = Number.parseInt(e.target.value) * 60
    setWorkTime(value)
    if (isWorkPhase) setTimeLeft(value)
  }

  const handleRestTimeChange = (e) => {
    const value = Number.parseInt(e.target.value) * 60
    setRestTime(value)
    if (!isWorkPhase) setTimeLeft(value)
  }

  const handleRoundsChange = (e) => {
    const value = Number.parseInt(e.target.value)
    setRounds(value)
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold text-center mb-8">Minuteur d'entraînement</h1>

          <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
            {/* Timer Display */}
            <div className="text-center mb-8">
              <div className="text-sm font-medium text-gray-500 mb-2">
                {isWorkPhase ? "TRAVAIL" : "REPOS"} • ROUND {currentRound}/{rounds}
              </div>
              <div className={`text-7xl font-bold mb-4 ${isWorkPhase ? "text-blue-600" : "text-orange-500"}`}>
                {formatTime(timeLeft)}
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5 mb-6">
                <div
                  className={`h-2.5 rounded-full ${isWorkPhase ? "bg-blue-600" : "bg-orange-500"}`}
                  style={{ width: `${(timeLeft / (isWorkPhase ? workTime : restTime)) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* Timer Controls */}
            <div className="flex justify-center space-x-4 mb-8">
              <button
                onClick={toggleTimer}
                className={`w-16 h-16 rounded-full flex items-center justify-center ${
                  isActive ? "bg-orange-500 hover:bg-orange-600" : "bg-blue-600 hover:bg-blue-700"
                } text-white transition-colors`}
              >
                {isActive ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8" />}
              </button>
              <button
                onClick={resetTimer}
                className="w-16 h-16 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
              >
                <RotateCcw className="h-6 w-6 text-gray-700" />
              </button>
              <button
                onClick={toggleSettings}
                className="w-16 h-16 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
              >
                <Settings className="h-6 w-6 text-gray-700" />
              </button>
            </div>

            {/* Settings Panel */}
            {showSettings && (
              <div className="border-t border-gray-200 pt-6">
                <h3 className="text-lg font-semibold mb-4">Paramètres du minuteur</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Temps de travail (minutes)</label>
                    <input
                      type="number"
                      min="1"
                      max="60"
                      value={workTime / 60}
                      onChange={handleWorkTimeChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Temps de repos (minutes)</label>
                    <input
                      type="number"
                      min="1"
                      max="60"
                      value={restTime / 60}
                      onChange={handleRestTimeChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Nombre de rounds</label>
                    <input
                      type="number"
                      min="1"
                      max="20"
                      value={rounds}
                      onChange={handleRoundsChange}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <button
                    onClick={toggleSettings}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Appliquer
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Workout Type Selection */}
          <div className="bg-white rounded-xl shadow-lg p-8">
            <h3 className="text-xl font-semibold mb-6">Préréglages d'entraînement</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
              <WorkoutPreset
                name="HIIT"
                work={45}
                rest={15}
                rounds={8}
                onClick={() => {
                  setWorkTime(45)
                  setRestTime(15)
                  setRounds(8)
                  setTimeLeft(45)
                  setIsWorkPhase(true)
                  setCurrentRound(1)
                  setIsActive(false)
                }}
              />
              <WorkoutPreset
                name="Tabata"
                work={20}
                rest={10}
                rounds={8}
                onClick={() => {
                  setWorkTime(20)
                  setRestTime(10)
                  setRounds(8)
                  setTimeLeft(20)
                  setIsWorkPhase(true)
                  setCurrentRound(1)
                  setIsActive(false)
                }}
              />
              <WorkoutPreset
                name="EMOM"
                work={60}
                rest={0}
                rounds={10}
                onClick={() => {
                  setWorkTime(60)
                  setRestTime(0)
                  setRounds(10)
                  setTimeLeft(60)
                  setIsWorkPhase(true)
                  setCurrentRound(1)
                  setIsActive(false)
                }}
              />
              <WorkoutPreset
                name="Pomodoro"
                work={25 * 60}
                rest={5 * 60}
                rounds={4}
                onClick={() => {
                  setWorkTime(25 * 60)
                  setRestTime(5 * 60)
                  setRounds(4)
                  setTimeLeft(25 * 60)
                  setIsWorkPhase(true)
                  setCurrentRound(1)
                  setIsActive(false)
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function WorkoutPreset({ name, work, rest, rounds, onClick }) {
  // Format work/rest time for display
  const formatPresetTime = (seconds) => {
    if (seconds < 60) return `${seconds}s`
    return `${Math.floor(seconds / 60)}m`
  }

  return (
    <button
      onClick={onClick}
      className="bg-gray-100 hover:bg-blue-50 border border-gray-200 rounded-lg p-4 text-left transition-colors"
    >
      <h4 className="font-bold text-blue-700">{name}</h4>
      <div className="text-sm text-gray-600 mt-2">
        <div>Travail: {formatPresetTime(work)}</div>
        <div>Repos: {formatPresetTime(rest)}</div>
        <div>Rounds: {rounds}</div>
      </div>
    </button>
  )
}

