document.addEventListener("DOMContentLoaded", () => {
  // Timer elements
  const timerClock = document.getElementById("timerClock")
  const timerPhase = document.getElementById("timerPhase")
  const timerProgressBar = document.getElementById("timerProgressBar")
  const currentRoundElement = document.getElementById("currentRound")
  const totalRoundsElement = document.getElementById("totalRounds")
  const timerToggle = document.getElementById("timerToggle")
  const timerToggleIcon = document.getElementById("timerToggleIcon")
  const timerReset = document.getElementById("timerReset")
  const timerSettings = document.getElementById("timerSettings")
  const timerSettingsPanel = document.getElementById("timerSettingsPanel")
  const applySettings = document.getElementById("applySettings")

  // Settings inputs
  const workTimeInput = document.getElementById("workTime")
  const restTimeInput = document.getElementById("restTime")
  const roundsInput = document.getElementById("rounds")

  // Timer state
  let workTime = 25 * 60 // 25 minutes in seconds
  let restTime = 5 * 60 // 5 minutes in seconds
  let timeLeft = workTime
  let isActive = false
  let isWorkPhase = true
  let rounds = 1
  let currentRound = 1
  let timerInterval = null

  // Initialize timer display
  updateTimerDisplay()

  // Timer toggle button
  timerToggle.addEventListener("click", () => {
    isActive = !isActive

    if (isActive) {
      timerToggleIcon.src = "img/icons/pause.svg"
      startTimer()
    } else {
      timerToggleIcon.src = "img/icons/play.svg"
      pauseTimer()
    }
  })

  // Timer reset button
  timerReset.addEventListener("click", () => {
    resetTimer()
  })

  // Timer settings button
  timerSettings.addEventListener("click", () => {
    timerSettingsPanel.style.display = timerSettingsPanel.style.display === "block" ? "none" : "block"

    // Pause timer if it's running
    if (isActive) {
      isActive = false
      timerToggleIcon.src = "img/icons/play.svg"
      pauseTimer()
    }
  })

  // Apply settings button
  applySettings.addEventListener("click", () => {
    workTime = Number.parseInt(workTimeInput.value) * 60
    restTime = Number.parseInt(restTimeInput.value) * 60
    rounds = Number.parseInt(roundsInput.value)

    // Reset timer with new settings
    resetTimer()

    // Hide settings panel
    timerSettingsPanel.style.display = "none"
  })

  // Preset buttons
  const presetButtons = document.querySelectorAll(".preset-card")
  presetButtons.forEach((button) => {
    button.addEventListener("click", function () {
      const work = Number.parseInt(this.getAttribute("data-work"))
      const rest = Number.parseInt(this.getAttribute("data-rest"))
      const presetRounds = Number.parseInt(this.getAttribute("data-rounds"))

      workTime = work
      restTime = rest
      rounds = presetRounds

      // Update settings
      workTimeInput.value = work / 60
      restTimeInput.value = rest / 60
      roundsInput.value = rounds

      // Reset timer with new settings
      resetTimer()
    })
  })

  // Timer functions
  function startTimer() {
    timerInterval = setInterval(() => {
      timeLeft--

      if (timeLeft <= 0) {
        clearInterval(timerInterval)

        // Phase completed
        if (isWorkPhase) {
          // Work phase completed, switch to rest
          isWorkPhase = false
          timeLeft = restTime
          timerPhase.textContent = `REPOS • ROUND ${currentRound}/${rounds}`
          startTimer()
        } else {
          // Rest phase completed
          if (currentRound < rounds) {
            // Move to next round
            currentRound++
            currentRoundElement.textContent = currentRound
            isWorkPhase = true
            timeLeft = workTime
            timerPhase.textContent = `TRAVAIL • ROUND ${currentRound}/${rounds}`
            startTimer()
          } else {
            // Workout completed
            alert("Entraînement terminé !")
            resetTimer()
          }
        }
      }

      updateTimerDisplay()
    }, 1000)
  }

  function pauseTimer() {
    clearInterval(timerInterval)
  }

  function resetTimer() {
    clearInterval(timerInterval)
    isActive = false
    isWorkPhase = true
    currentRound = 1
    timeLeft = workTime
    timerToggleIcon.src = "img/icons/play.svg"
    timerPhase.textContent = `TRAVAIL • ROUND ${currentRound}/${rounds}`
    currentRoundElement.textContent = currentRound
    totalRoundsElement.textContent = rounds
    updateTimerDisplay()
  }

  function updateTimerDisplay() {
    // Update timer clock
    const minutes = Math.floor(timeLeft / 60)
    const seconds = timeLeft % 60
    timerClock.textContent = `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`

    // Update progress bar
    const totalTime = isWorkPhase ? workTime : restTime
    const progressPercentage = (timeLeft / totalTime) * 100
    timerProgressBar.style.width = `${progressPercentage}%`

    // Update timer color based on phase
    if (isWorkPhase) {
      timerClock.style.color = "var(--primary-color)"
      timerProgressBar.style.backgroundColor = "var(--primary-color)"
    } else {
      timerClock.style.color = "var(--secondary-color)"
      timerProgressBar.style.backgroundColor = "var(--secondary-color)"
    }
  }
})

