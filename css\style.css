/* Base Styles */
:root {
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #60a5fa;
  --secondary-color: #f97316;
  --secondary-dark: #ea580c;
  --secondary-light: #fb923c;
  --text-color: #333333;
  --text-light: #666666;
  --background-color: #f5f7fa;
  --background-light: #ffffff;
  --border-color: #e5e7eb;
  --success-color: #22c55e;
  --error-color: #ef4444;
  --border-radius: 8px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", sans-serif;
  color: var(--text-color);
  background-color: var(--background-color);
  line-height: 1.6;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

a {
  text-decoration: none;
  color: var(--primary-color);
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
}

ul {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
}

.icon {
  display: inline-block;
  vertical-align: middle;
  margin-left: 5px;
}

.text-center {
  text-align: center;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-bottom: 0.5em;
  font-weight: 700;
  line-height: 1.2;
}

.page-title {
  font-size: 2rem;
  text-align: center;
  margin-bottom: 1rem;
}

.page-description {
  text-align: center;
  color: var(--text-light);
  max-width: 800px;
  margin: 0 auto 2rem;
}

.section-title {
  font-size: 1.75rem;
  text-align: center;
  margin-bottom: 2rem;
}

/* Buttons */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-weight: 600;
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  border: none;
}

.btn-primary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--secondary-dark);
  color: white;
}

.btn-secondary {
  background-color: white;
  color: var(--primary-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--background-color);
}

/* Navigation */
.navbar {
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.logo {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.logo span {
  color: var(--secondary-color);
}

.navbar-nav {
  display: flex;
}

.navbar-nav li {
  margin-left: 1.5rem;
}

.navbar-nav a {
  color: var(--text-color);
  font-weight: 500;
}

.navbar-nav a:hover,
.navbar-nav a.active {
  color: var(--primary-color);
}

.navbar-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.navbar-toggle span {
  width: 25px;
  height: 3px;
  background-color: var(--text-color);
  margin: 2px 0;
  transition: var(--transition);
}

/* Hero Section */
.hero {
  padding: 4rem 0;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  color: white;
}

.hero .container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.hero-content {
  flex: 1;
  min-width: 300px;
  padding-right: 2rem;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.1rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.hero-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.hero-image {
  flex: 1;
  min-width: 300px;
  text-align: center;
}

.hero-image img {
  max-width: 100%;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

/* Features Preview */
.features-preview {
  padding: 4rem 0;
  background-color: var(--background-light);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.feature-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-icon {
  margin-bottom: 1rem;
}

.feature-icon img {
  width: 50px;
  height: 50px;
}

.feature-card h3 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.feature-link {
  display: inline-block;
  margin-top: 1rem;
  color: var(--secondary-color);
  font-weight: 500;
}

.feature-link:hover {
  color: var(--secondary-dark);
}

/* Call to Action */
.cta {
  padding: 4rem 0;
  background-color: var(--primary-color);
  color: white;
  text-align: center;
}

.cta h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.cta p {
  max-width: 600px;
  margin: 0 auto 2rem;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Testimonials */
.testimonials {
  padding: 4rem 0;
  background-color: var(--background-light);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.testimonial-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
}

.testimonial-quote {
  font-style: italic;
  margin-bottom: 1.5rem;
  color: var(--text-light);
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.testimonial-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-light);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  margin-right: 1rem;
}

.testimonial-name {
  font-weight: 600;
}

.testimonial-role {
  font-size: 0.875rem;
  color: var(--text-light);
}

/* Footer */
.footer {
  background-color: var(--primary-dark);
  color: white;
  padding: 4rem 0 2rem;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-brand {
  grid-column: 1 / -1;
}

.footer-logo {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.footer-logo span {
  color: var(--secondary-light);
}

.footer-brand p {
  max-width: 400px;
  opacity: 0.8;
}

.footer-links h4 {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.footer-links ul li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: rgba(255, 255, 255, 0.8);
}

.footer-links a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.875rem;
  opacity: 0.7;
}

/* Timer Page */
.timer-section {
  padding: 4rem 0;
}

.timer-container {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin-bottom: 2rem;
}

.timer-display {
  text-align: center;
  margin-bottom: 2rem;
}

.timer-phase {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-light);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.timer-clock {
  font-size: 5rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: 1rem;
}

.timer-progress {
  height: 8px;
  background-color: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
}

.timer-progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  width: 100%;
  transition: width 1s linear;
}

.timer-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.timer-btn {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--background-color);
  border: none;
  cursor: pointer;
  transition: var(--transition);
}

.timer-btn:hover {
  background-color: var(--border-color);
}

.timer-btn-primary {
  background-color: var(--primary-color);
}

.timer-btn-primary:hover {
  background-color: var(--primary-dark);
}

.timer-btn img {
  width: 24px;
  height: 24px;
}

.timer-settings {
  border-top: 1px solid var(--border-color);
  padding-top: 2rem;
  display: none;
}

.timer-settings h3 {
  margin-bottom: 1.5rem;
}

.timer-settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.timer-setting label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.timer-setting input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.workout-presets {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
}

.presets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.preset-card {
  background-color: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  text-align: left;
  cursor: pointer;
  transition: var(--transition);
}

.preset-card:hover {
  background-color: var(--primary-light);
  color: white;
}

.preset-card h4 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.preset-card:hover h4 {
  color: white;
}

.preset-details {
  font-size: 0.875rem;
  color: var(--text-light);
}

.preset-card:hover .preset-details {
  color: rgba(255, 255, 255, 0.9);
}

/* Programs Page */
.programs-section {
  padding: 4rem 0;
}

.programs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.program-category {
  background-color: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.program-header {
  padding: 1.5rem;
  display: flex;
  align-items: flex-start;
}

.program-icon {
  margin-right: 1rem;
}

.program-icon img {
  width: 40px;
  height: 40px;
}

.program-title h2 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.program-title p {
  color: var(--text-light);
  font-size: 0.875rem;
}

.program-list {
  border-top: 1px solid var(--border-color);
}

.program-item {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.program-item:last-child {
  border-bottom: none;
}

.program-item-details h3 {
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.program-item-details p {
  font-size: 0.875rem;
  color: var(--text-light);
}

.program-link {
  color: var(--secondary-color);
  font-weight: 500;
  font-size: 0.875rem;
}

.program-guide {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin-bottom: 3rem;
}

.program-guide h2 {
  text-align: center;
  margin-bottom: 2rem;
}

.guide-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.guide-step {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
}

.step-number {
  width: 30px;
  height: 30px;
  background-color: var(--primary-light);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  margin-bottom: 1rem;
}

/* Progress Page */
.progress-section {
  padding: 4rem 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
}

.stat-icon {
  margin-right: 1rem;
}

.stat-icon img {
  width: 30px;
  height: 30px;
}

.stat-content h3 {
  font-size: 0.875rem;
  color: var(--text-light);
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-description {
  font-size: 0.75rem;
  color: var(--text-light);
}

.charts-container {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin-bottom: 3rem;
}

.charts-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 2rem;
}

.chart-tab {
  padding: 0.75rem 1.5rem;
  background: none;
  border: none;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-light);
  border-bottom: 2px solid transparent;
  transition: var(--transition);
}

.chart-tab:hover {
  color: var(--primary-color);
}

.chart-tab.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.chart-panel {
  display: none;
}

.chart-panel.active {
  display: block;
}

.chart-panel h3 {
  margin-bottom: 0.25rem;
}

.chart-panel p {
  color: var(--text-light);
  font-size: 0.875rem;
  margin-bottom: 1.5rem;
}

.chart-container {
  height: 300px;
}

.add-workout {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
}

.add-workout h2 {
  margin-bottom: 1.5rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
}

.form-submit {
  text-align: right;
}

/* Features Page */
.features-section {
  padding: 4rem 0;
}

.main-features {
  margin-bottom: 3rem;
}

.feature-large {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: flex-start;
  margin-bottom: 2rem;
}

.feature-content {
  flex: 1;
}

.feature-content h2 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.additional-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.feature-small {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
}

.feature-icon-small {
  margin-bottom: 1rem;
}

.feature-icon-small img {
  width: 30px;
  height: 30px;
}

.feature-small h3 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.feature-comparison {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin-bottom: 3rem;
}

.feature-comparison h2 {
  text-align: center;
  margin-bottom: 2rem;
}

.table-container {
  overflow-x: auto;
}

.comparison-table {
  width: 100%;
  border-collapse: collapse;
}

.comparison-table th,
.comparison-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.comparison-table th {
  font-weight: 600;
  background-color: var(--background-color);
}

.comparison-table td.yes {
  color: var(--success-color);
  font-weight: 700;
}

.comparison-table td.no {
  color: var(--error-color);
  font-weight: 700;
}

.faq-section {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
}

.faq-section h2 {
  text-align: center;
  margin-bottom: 2rem;
}

.faq-item {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 1.5rem;
  margin-bottom: 1.5rem;
}

.faq-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.faq-item h3 {
  margin-bottom: 0.5rem;
}

/* About Page */
.about-section {
  padding: 4rem 0;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.benefit-card {
  background-color: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.benefit-image {
  height: 200px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  display: flex;
  align-items: center;
  justify-content: center;
}

.benefit-image img {
  width: 80px;
  height: 80px;
}

.benefit-content {
  padding: 2rem;
}

.benefit-list {
  margin-top: 1rem;
}

.benefit-list li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.benefit-list li:before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--success-color);
  font-weight: 700;
}

.privacy-section {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
  margin-bottom: 3rem;
}

.privacy-icon {
  margin-right: 2rem;
}

.privacy-icon img {
  width: 100px;
  height: 100px;
}

.privacy-content {
  flex: 1;
}

.testimonials-section {
  margin-bottom: 3rem;
}

.about-cta {
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  border-radius: var(--border-radius);
  padding: 3rem 2rem;
  text-align: center;
  color: white;
  box-shadow: var(--box-shadow);
}

.about-cta h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
}

.about-cta p {
  max-width: 600px;
  margin: 0 auto 2rem;
  font-size: 1.1rem;
  opacity: 0.9;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .navbar-menu {
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    display: none;
  }

  .navbar-menu.active {
    display: block;
  }

  .navbar-nav {
    flex-direction: column;
  }

  .navbar-nav li {
    margin: 0;
  }

  .navbar-nav a {
    display: block;
    padding: 0.75rem 2rem;
  }

  .navbar-toggle {
    display: flex;
  }

  .hero-content,
  .hero-image {
    flex: 100%;
    padding-right: 0;
  }

  .hero-content {
    margin-bottom: 2rem;
  }

  .feature-large {
    flex-direction: column;
  }

  .feature-icon {
    margin-bottom: 1rem;
  }

  .privacy-section {
    flex-direction: column;
    text-align: center;
  }

  .privacy-icon {
    margin-right: 0;
    margin-bottom: 1.5rem;
  }
}

