document.addEventListener("DOMContentLoaded", () => {
  // Load workout data from localStorage
  let workoutData = JSON.parse(localStorage.getItem("workoutData")) || []
  let weightData = JSON.parse(localStorage.getItem("weightData")) || []

  // Update stats
  updateStats()

  // Initialize charts
  initCharts()

  // Tab switching
  const chartTabs = document.querySelectorAll(".chart-tab")
  const chartPanels = document.querySelectorAll(".chart-panel")

  chartTabs.forEach((tab) => {
    tab.addEventListener("click", function () {
      const tabId = this.getAttribute("data-tab")

      // Remove active class from all tabs and panels
      chartTabs.forEach((t) => t.classList.remove("active"))
      chartPanels.forEach((p) => p.classList.remove("active"))

      // Add active class to current tab and panel
      this.classList.add("active")
      document.getElementById(`${tabId}Chart`).classList.add("active")
    })
  })

  // Workout form submission
  const workoutForm = document.getElementById("workoutForm")

  workoutForm.addEventListener("submit", (e) => {
    e.preventDefault()

    // Get form values
    const date = document.getElementById("workoutDate").value
    const type = document.getElementById("workoutType").value
    const duration = Number.parseInt(document.getElementById("workoutDuration").value)
    const calories = Number.parseInt(document.getElementById("workoutCalories").value)

    // Format date for display
    const displayDate = new Date(date).toLocaleDateString("fr-FR", {
      day: "2-digit",
      month: "2-digit",
    })

    // Create workout object
    const workout = {
      date: displayDate,
      type: type,
      duration: duration,
      calories: calories,
    }

    // Add to workout data
    workoutData.push(workout)

    // Save to localStorage
    localStorage.setItem("workoutData", JSON.stringify(workoutData))

    // Update stats and charts
    updateStats()
    initCharts()

    // Reset form
    workoutForm.reset()

    // Show success message
    alert("Entraînement ajouté avec succès !")
  })

  // Functions
  function updateStats() {
    const totalWorkouts = workoutData.length
    const totalDuration = workoutData.reduce((sum, workout) => sum + workout.duration, 0)
    const totalCalories = workoutData.reduce((sum, workout) => sum + workout.calories, 0)

    document.getElementById("totalWorkouts").textContent = totalWorkouts
    document.getElementById("totalDuration").textContent = totalDuration
    document.getElementById("totalCalories").textContent = totalCalories

    // Calculate weight change
    if (weightData.length >= 2) {
      const firstWeight = weightData[0].weight
      const lastWeight = weightData[weightData.length - 1].weight
      const weightChange = (lastWeight - firstWeight).toFixed(1)

      document.getElementById("weightChange").textContent = `${weightChange} kg`

      if (weightChange < 0) {
        document.getElementById("weightTrend").textContent = "Perte de poids"
      } else if (weightChange > 0) {
        document.getElementById("weightTrend").textContent = "Gain de poids"
      } else {
        document.getElementById("weightTrend").textContent = "Poids stable"
      }
    }
  }

  function initCharts() {
    // Sample data if no data exists
    if (workoutData.length === 0) {
      workoutData = [
        { date: "01/03", duration: 45, calories: 320, type: "musculation" },
        { date: "03/03", duration: 30, calories: 250, type: "hiit" },
        { date: "05/03", duration: 60, calories: 450, type: "cardio" },
        { date: "08/03", duration: 40, calories: 300, type: "musculation" },
        { date: "10/03", duration: 35, calories: 280, type: "hiit" },
        { date: "12/03", duration: 50, calories: 380, type: "cardio" },
        { date: "15/03", duration: 55, calories: 420, type: "musculation" },
      ]
    }

    if (weightData.length === 0) {
      weightData = [
        { date: "01/03", weight: 75.5 },
        { date: "08/03", weight: 74.8 },
        { date: "15/03", weight: 74.2 },
      ]
    }

    // Create charts using canvas
    createWorkoutsChart()
    createWeightChart()
    createPerformanceChart()
  }

  function createWorkoutsChart() {
    const canvas = document.getElementById("workoutsCanvas")
    const ctx = canvas.getContext("2d")

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Set canvas dimensions
    canvas.width = canvas.parentElement.clientWidth
    canvas.height = 300

    // Chart dimensions
    const chartWidth = canvas.width - 60
    const chartHeight = canvas.height - 60
    const barWidth = chartWidth / workoutData.length / 2 - 10

    // Draw axes
    ctx.beginPath()
    ctx.moveTo(40, 20)
    ctx.lineTo(40, chartHeight + 40)
    ctx.lineTo(chartWidth + 50, chartHeight + 40)
    ctx.stroke()

    // Draw data
    const maxDuration = Math.max(...workoutData.map((w) => w.duration))
    const maxCalories = Math.max(...workoutData.map((w) => w.calories))

    workoutData.forEach((workout, index) => {
      const x = 50 + index * (chartWidth / workoutData.length)

      // Duration bar
      const durationHeight = (workout.duration / maxDuration) * chartHeight
      ctx.fillStyle = "var(--primary-color)"
      ctx.fillRect(x, chartHeight + 40 - durationHeight, barWidth, durationHeight)

      // Calories bar
      const caloriesHeight = (workout.calories / maxCalories) * chartHeight
      ctx.fillStyle = "var(--secondary-color)"
      ctx.fillRect(x + barWidth + 5, chartHeight + 40 - caloriesHeight, barWidth, caloriesHeight)

      // Date label
      ctx.fillStyle = "var(--text-color)"
      ctx.font = "10px Arial"
      ctx.textAlign = "center"
      ctx.fillText(workout.date, x + barWidth, chartHeight + 55)
    })

    // Legend
    ctx.fillStyle = "var(--primary-color)"
    ctx.fillRect(chartWidth - 100, 20, 15, 15)
    ctx.fillStyle = "var(--text-color)"
    ctx.textAlign = "left"
    ctx.fillText("Durée (min)", chartWidth - 80, 30)

    ctx.fillStyle = "var(--secondary-color)"
    ctx.fillRect(chartWidth - 100, 45, 15, 15)
    ctx.fillStyle = "var(--text-color)"
    ctx.fillText("Calories", chartWidth - 80, 55)
  }

  function createWeightChart() {
    const canvas = document.getElementById("weightCanvas")
    const ctx = canvas.getContext("2d")

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Set canvas dimensions
    canvas.width = canvas.parentElement.clientWidth
    canvas.height = 300

    // Chart dimensions
    const chartWidth = canvas.width - 60
    const chartHeight = canvas.height - 60

    // Draw axes
    ctx.beginPath()
    ctx.moveTo(40, 20)
    ctx.lineTo(40, chartHeight + 40)
    ctx.lineTo(chartWidth + 50, chartHeight + 40)
    ctx.stroke()

    // Draw data
    const weights = weightData.map((w) => w.weight)
    const minWeight = Math.min(...weights) - 1
    const maxWeight = Math.max(...weights) + 1
    const weightRange = maxWeight - minWeight

    // Draw line
    ctx.beginPath()
    weightData.forEach((data, index) => {
      const x = 50 + index * (chartWidth / (weightData.length - 1))
      const y = chartHeight + 40 - ((data.weight - minWeight) / weightRange) * chartHeight

      if (index === 0) {
        ctx.moveTo(x, y)
      } else {
        ctx.lineTo(x, y)
      }

      // Draw point
      ctx.fillStyle = "var(--primary-color)"
      ctx.beginPath()
      ctx.arc(x, y, 5, 0, Math.PI * 2)
      ctx.fill()

      // Draw weight value
      ctx.fillStyle = "var(--text-color)"
      ctx.font = "12px Arial"
      ctx.textAlign = "center"
      ctx.fillText(data.weight.toFixed(1), x, y - 10)

      // Date label
      ctx.fillText(data.date, x, chartHeight + 55)
    })

    ctx.strokeStyle = "var(--primary-color)"
    ctx.lineWidth = 2
    ctx.stroke()
  }

  function createPerformanceChart() {
    const canvas = document.getElementById("performanceCanvas")
    const ctx = canvas.getContext("2d")

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Set canvas dimensions
    canvas.width = canvas.parentElement.clientWidth
    canvas.height = 300

    // Chart dimensions
    const chartWidth = canvas.width - 60
    const chartHeight = canvas.height - 60

    // Calculate workout types
    const workoutTypes = {
      musculation: workoutData.filter((w) => w.type === "musculation").length,
      cardio: workoutData.filter((w) => w.type === "cardio").length,
      hiit: workoutData.filter((w) => w.type === "hiit").length,
      mobilite: workoutData.filter((w) => w.type === "mobilite").length,
    }

    const types = Object.keys(workoutTypes)
    const maxCount = Math.max(...Object.values(workoutTypes))

    // Draw axes
    ctx.beginPath()
    ctx.moveTo(40, 20)
    ctx.lineTo(40, chartHeight + 40)
    ctx.lineTo(chartWidth + 50, chartHeight + 40)
    ctx.stroke()

    // Draw bars
    const barWidth = chartWidth / types.length / 2

    types.forEach((type, index) => {
      const count = workoutTypes[type]
      const x = 60 + index * (chartWidth / types.length)
      const barHeight = (count / maxCount) * chartHeight

      // Bar
      ctx.fillStyle = "var(--primary-color)"
      ctx.fillRect(x, chartHeight + 40 - barHeight, barWidth, barHeight)

      // Type label
      ctx.fillStyle = "var(--text-color)"
      ctx.font = "12px Arial"
      ctx.textAlign = "center"
      ctx.fillText(type.charAt(0).toUpperCase() + type.slice(1), x + barWidth / 2, chartHeight + 55)

      // Count label
      ctx.fillText(count, x + barWidth / 2, chartHeight + 40 - barHeight - 10)
    })
  }
})

