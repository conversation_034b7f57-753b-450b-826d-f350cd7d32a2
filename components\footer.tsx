import Link from "next/link"

export default function Footer() {
  return (
    <footer className="bg-blue-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold mb-4">
              Fit<span className="text-orange-400">Tracker</span>
            </h3>
            <p className="text-blue-200 mb-4">
              Suivez vos entraînements, définissez des objectifs et visualisez votre progression en un seul endroit.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-4">Liens rapides</h4>
            <ul className="space-y-2">
              <FooterLink href="/" label="Accueil" />
              <FooterLink href="/timer" label="Minuteur" />
              <FooterLink href="/programs" label="Programmes" />
              <FooterLink href="/progress" label="Progression" />
              <FooterLink href="/features" label="Fonctionnalités" />
              <FooterLink href="/about" label="À propos" />
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-4">Support</h4>
            <ul className="space-y-2">
              <FooterLink href="/faq" label="FAQ" />
              <FooterLink href="/contact" label="Contact" />
              <FooterLink href="/help" label="Aide" />
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-semibold mb-4">Légal</h4>
            <ul className="space-y-2">
              <FooterLink href="/terms" label="Conditions d'utilisation" />
              <FooterLink href="/privacy" label="Politique de confidentialité" />
              <FooterLink href="/cookies" label="Cookies" />
            </ul>
          </div>
        </div>

        <div className="border-t border-blue-800 mt-8 pt-8 text-center text-blue-300">
          <p>&copy; {new Date().getFullYear()} FitTracker. Tous droits réservés.</p>
        </div>
      </div>
    </footer>
  )
}

function FooterLink({ href, label }) {
  return (
    <li>
      <Link href={href} className="text-blue-300 hover:text-orange-300 transition-colors">
        {label}
      </Link>
    </li>
  )
}

