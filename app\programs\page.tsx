import Link from "next/link"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Move } from "lucide-react"

export default function ProgramsPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-bold text-center mb-4">Programmes d'entraînement</h1>
        <p className="text-gray-600 text-center max-w-2xl mx-auto mb-12">
          Découvrez nos programmes d'entraînement catégorisés pour atteindre vos objectifs fitness, que vous soyez
          débutant ou athlète confirmé.
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
          <ProgramCategory
            icon={<Dumbbell className="h-12 w-12 text-blue-600" />}
            title="Musculation"
            description="Programmes conçus pour développer votre force et votre masse musculaire avec des exercices ciblés."
            programs={[
              { name: "Débutant - Full Body", duration: "8 semaines", sessions: 3 },
              { name: "Intermédiaire - Split 4 jours", duration: "12 semaines", sessions: 4 },
              { name: "Avancé - PPL", duration: "16 semaines", sessions: 6 },
            ]}
          />

          <ProgramCategory
            icon={<Heart className="h-12 w-12 text-blue-600" />}
            title="Cardio"
            description="Améliorez votre endurance cardiovasculaire et brûlez des calories avec ces programmes efficaces."
            programs={[
              { name: "Débutant - Marche/Course", duration: "6 semaines", sessions: 3 },
              { name: "Intermédiaire - Interval Training", duration: "8 semaines", sessions: 4 },
              { name: "Avancé - Endurance", duration: "12 semaines", sessions: 5 },
            ]}
          />

          <ProgramCategory
            icon={<Zap className="h-12 w-12 text-blue-600" />}
            title="HIIT"
            description="Entraînements à haute intensité pour maximiser les résultats en un minimum de temps."
            programs={[
              { name: "Débutant - HIIT Corps Entier", duration: "4 semaines", sessions: 2 },
              { name: "Intermédiaire - Tabata", duration: "6 semaines", sessions: 3 },
              { name: "Avancé - HIIT Complexe", duration: "8 semaines", sessions: 4 },
            ]}
          />

          <ProgramCategory
            icon={<Move className="h-12 w-12 text-blue-600" />}
            title="Mobilité"
            description="Améliorez votre flexibilité, votre amplitude de mouvement et prévenez les blessures."
            programs={[
              { name: "Débutant - Étirements Basiques", duration: "4 semaines", sessions: 3 },
              { name: "Intermédiaire - Yoga Fitness", duration: "6 semaines", sessions: 4 },
              { name: "Avancé - Mobilité Complète", duration: "8 semaines", sessions: 5 },
            ]}
          />
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8 mb-12">
          <h2 className="text-2xl font-bold mb-6">Comment choisir votre programme</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="border border-gray-200 rounded-lg p-5">
              <div className="bg-blue-100 text-blue-700 rounded-full w-8 h-8 flex items-center justify-center font-bold mb-4">
                1
              </div>
              <h3 className="font-bold text-lg mb-2">Évaluez votre niveau</h3>
              <p className="text-gray-600">
                Choisissez un programme adapté à votre niveau actuel pour progresser efficacement sans risque de
                blessure.
              </p>
            </div>
            <div className="border border-gray-200 rounded-lg p-5">
              <div className="bg-blue-100 text-blue-700 rounded-full w-8 h-8 flex items-center justify-center font-bold mb-4">
                2
              </div>
              <h3 className="font-bold text-lg mb-2">Définissez vos objectifs</h3>
              <p className="text-gray-600">
                Perte de poids, prise de muscle, endurance... Sélectionnez un programme aligné avec vos objectifs
                personnels.
              </p>
            </div>
            <div className="border border-gray-200 rounded-lg p-5">
              <div className="bg-blue-100 text-blue-700 rounded-full w-8 h-8 flex items-center justify-center font-bold mb-4">
                3
              </div>
              <h3 className="font-bold text-lg mb-2">Planifiez votre emploi du temps</h3>
              <p className="text-gray-600">
                Assurez-vous que le nombre de sessions hebdomadaires est compatible avec votre disponibilité.
              </p>
            </div>
          </div>
        </div>

        <div className="text-center">
          <Link
            href="/progress"
            className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-8 rounded-lg inline-flex items-center transition-colors"
          >
            Suivre votre progression <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </div>
  )
}

function ProgramCategory({ icon, title, description, programs }) {
  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      <div className="p-6">
        <div className="flex items-start mb-4">
          <div className="mr-4">{icon}</div>
          <div>
            <h2 className="text-2xl font-bold text-blue-800 mb-2">{title}</h2>
            <p className="text-gray-600">{description}</p>
          </div>
        </div>

        <div className="mt-6 space-y-4">
          {programs.map((program, index) => (
            <div key={index} className="border-t border-gray-100 pt-4">
              <div className="flex justify-between items-center">
                <h3 className="font-semibold">{program.name}</h3>
                <Link
                  href={`/programs/${title.toLowerCase()}/${index + 1}`}
                  className="text-orange-500 hover:text-orange-600 flex items-center text-sm font-medium"
                >
                  Détails <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {program.duration} • {program.sessions} sessions/semaine
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

