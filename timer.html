<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minuteur d'entraînement - FitTracker</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <div class="navbar-brand">
                <a href="index.html" class="logo">Fit<span>Tracker</span></a>
            </div>
            <div class="navbar-menu" id="navbarMenu">
                <ul class="navbar-nav">
                    <li><a href="index.html">Accueil</a></li>
                    <li><a href="timer.html" class="active">Minuteur</a></li>
                    <li><a href="programs.html">Programmes</a></li>
                    <li><a href="progress.html">Progression</a></li>
                    <li><a href="features.html">Fonctionnalités</a></li>
                    <li><a href="about.html">À propos</a></li>
                </ul>
            </div>
            <div class="navbar-toggle" id="navbarToggle">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Timer Section -->
    <section class="timer-section">
        <div class="container">
            <h1 class="page-title">Minuteur d'entraînement</h1>
            
            <div class="timer-container">
                <div class="timer-display">
                    <div class="timer-phase" id="timerPhase">TRAVAIL • ROUND <span id="currentRound">1</span>/<span id="totalRounds">1</span></div>
                    <div class="timer-clock" id="timerClock">25:00</div>
                    <div class="timer-progress">
                        <div class="timer-progress-bar" id="timerProgressBar"></div>
                    </div>
                </div>
                
                <div class="timer-controls">
                    <button class="timer-btn timer-btn-primary" id="timerToggle">
                        <img src="img/icons/play.svg" alt="Play" id="timerToggleIcon">
                    </button>
                    <button class="timer-btn" id="timerReset">
                        <img src="img/icons/reset.svg" alt="Reset">
                    </button>
                    <button class="timer-btn" id="timerSettings">
                        <img src="img/icons/settings.svg" alt="Settings">
                    </button>
                </div>
                
                <div class="timer-settings" id="timerSettingsPanel">
                    <h3>Paramètres du minuteur</h3>
                    <div class="timer-settings-grid">
                        <div class="timer-setting">
                            <label for="workTime">Temps de travail (minutes)</label>
                            <input type="number" id="workTime" min="1" max="60" value="25">
                        </div>
                        <div class="timer-setting">
                            <label for="restTime">Temps de repos (minutes)</label>
                            <input type="number" id="restTime" min="1" max="60" value="5">
                        </div>
                        <div class="timer-setting">
                            <label for="rounds">Nombre de rounds</label>
                            <input type="number" id="rounds" min="1" max="20" value="1">
                        </div>
                    </div>
                    <button class="btn btn-primary" id="applySettings">Appliquer</button>
                </div>
            </div>
            
            <div class="workout-presets">
                <h3>Préréglages d'entraînement</h3>
                <div class="presets-grid">
                    <button class="preset-card" data-work="45" data-rest="15" data-rounds="8">
                        <h4>HIIT</h4>
                        <div class="preset-details">
                            <div>Travail: 45s</div>
                            <div>Repos: 15s</div>
                            <div>Rounds: 8</div>
                        </div>
                    </button>
                    <button class="preset-card" data-work="20" data-rest="10" data-rounds="8">
                        <h4>Tabata</h4>
                        <div class="preset-details">
                            <div>Travail: 20s</div>
                            <div>Repos: 10s</div>
                            <div>Rounds: 8</div>
                        </div>
                    </button>
                    <button class="preset-card" data-work="60" data-rest="0" data-rounds="10">
                        <h4>EMOM</h4>
                        <div class="preset-details">
                            <div>Travail: 60s</div>
                            <div>Repos: 0s</div>
                            <div>Rounds: 10</div>
                        </div>
                    </button>
                    <button class="preset-card" data-work="1500" data-rest="300" data-rounds="4">
                        <h4>Pomodoro</h4>
                        <div class="preset-details">
                            <div>Travail: 25m</div>
                            <div>Repos: 5m</div>
                            <div>Rounds: 4</div>
                        </div>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-grid">
                <div class="footer-brand">
                    <h3 class="footer-logo">Fit<span>Tracker</span></h3>
                    <p>Suivez vos entraînements, définissez des objectifs et visualisez votre progression en un seul endroit.</p>
                </div>
                <div class="footer-links">
                    <h4>Liens rapides</h4>
                    <ul>
                        <li><a href="index.html">Accueil</a></li>
                        <li><a href="timer.html">Minuteur</a></li>
                        <li><a href="programs.html">Programmes</a></li>
                        <li><a href="progress.html">Progression</a></li>
                        <li><a href="features.html">Fonctionnalités</a></li>
                        <li><a href="about.html">À propos</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h4>Support</h4>
                    <ul>
                        <li><a href="#">FAQ</a></li>
                        <li><a href="#">Contact</a></li>
                        <li><a href="#">Aide</a></li>
                    </ul>
                </div>
                <div class="footer-links">
                    <h4>Légal</h4>
                    <ul>
                        <li><a href="#">Conditions d'utilisation</a></li>
                        <li><a href="#">Politique de confidentialité</a></li>
                        <li><a href="#">Cookies</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <span id="currentYear"></span> FitTracker. Tous droits réservés.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script src="js/timer.js"></script>
</body>
</html>

