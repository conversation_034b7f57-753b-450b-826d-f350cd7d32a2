import Link from "next/link"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Info } from "lucide-react"

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <h1 className="text-4xl md:text-5xl font-bold mb-4">Transformez votre routine fitness avec FitTracker</h1>
              <p className="text-xl mb-8">
                Suivez vos entraînements, définissez des objectifs et visualisez votre progression en un seul endroit.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href="/timer"
                  className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-6 rounded-lg flex items-center justify-center transition-colors"
                >
                  Commencer maintenant <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
                <Link
                  href="/features"
                  className="bg-white hover:bg-gray-100 text-blue-800 font-bold py-3 px-6 rounded-lg flex items-center justify-center transition-colors"
                >
                  Découvrir les fonctionnalités
                </Link>
              </div>
            </div>
            <div className="md:w-1/2">
              <img
                src="/placeholder.svg?height=400&width=500"
                alt="FitTracker App Preview"
                className="rounded-lg shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Features Preview */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Fonctionnalités principales</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <FeatureCard
              icon={<Clock className="h-10 w-10 text-orange-500" />}
              title="Minuteur d'entraînement"
              description="Configurez différents intervalles pour vos séances HIIT, Tabata ou autres."
              link="/timer"
            />
            <FeatureCard
              icon={<Dumbbell className="h-10 w-10 text-orange-500" />}
              title="Programmes d'entraînement"
              description="Accédez à des programmes catégorisés pour musculation, cardio, HIIT et mobilité."
              link="/programs"
            />
            <FeatureCard
              icon={<BarChart3 className="h-10 w-10 text-orange-500" />}
              title="Suivi de progression"
              description="Visualisez votre progression avec des graphiques et statistiques détaillés."
              link="/progress"
            />
            <FeatureCard
              icon={<Info className="h-10 w-10 text-orange-500" />}
              title="Pourquoi FitTracker"
              description="Découvrez pourquoi des milliers d'utilisateurs nous font confiance."
              link="/about"
            />
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-blue-700 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Prêt à transformer votre routine fitness ?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Rejoignez des milliers d'utilisateurs qui ont déjà amélioré leurs performances grâce à FitTracker.
          </p>
          <Link
            href="/timer"
            className="bg-orange-500 hover:bg-orange-600 text-white font-bold py-3 px-8 rounded-lg inline-flex items-center transition-colors"
          >
            Commencer gratuitement <ArrowRight className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">Ce que disent nos utilisateurs</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <TestimonialCard
              quote="FitTracker a complètement changé ma façon de m'entraîner. Je peux enfin suivre mes progrès efficacement."
              author="Sophie M."
              role="Utilisatrice depuis 6 mois"
            />
            <TestimonialCard
              quote="Le minuteur d'entraînement est parfait pour mes séances HIIT. Simple à utiliser et très efficace."
              author="Thomas L."
              role="Coach sportif"
            />
            <TestimonialCard
              quote="J'adore pouvoir visualiser ma progression sur les graphiques. Très motivant pour continuer !"
              author="Julie D."
              role="Utilisatrice depuis 1 an"
            />
          </div>
        </div>
      </section>
    </div>
  )
}

function FeatureCard({ icon, title, description, link }) {
  return (
    <Link href={link} className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
      <div className="mb-4">{icon}</div>
      <h3 className="text-xl font-bold mb-2 text-blue-800">{title}</h3>
      <p className="text-gray-600 mb-4">{description}</p>
      <div className="text-orange-500 font-medium flex items-center">
        En savoir plus <ArrowRight className="ml-1 h-4 w-4" />
      </div>
    </Link>
  )
}

function TestimonialCard({ quote, author, role }) {
  return (
    <div className="bg-gray-50 p-6 rounded-lg">
      <p className="text-gray-700 mb-4 italic">"{quote}"</p>
      <div className="flex items-center">
        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold mr-3">
          {author.charAt(0)}
        </div>
        <div>
          <p className="font-bold">{author}</p>
          <p className="text-sm text-gray-500">{role}</p>
        </div>
      </div>
    </div>
  )
}

