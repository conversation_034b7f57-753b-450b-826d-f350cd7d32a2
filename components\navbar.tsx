"use client"

import Link from "next/link"
import { useState } from "react"
import { Menu, X } from "lucide-react"

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen)
  }

  return (
    <nav className="bg-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <span className="text-2xl font-bold text-blue-700">
                Fit<span className="text-orange-500">Tracker</span>
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <NavLink href="/" label="Accueil" />
            <NavLink href="/timer" label="Minuteur" />
            <NavLink href="/programs" label="Programmes" />
            <NavLink href="/progress" label="Progression" />
            <NavLink href="/features" label="Fonctionnalités" />
            <NavLink href="/about" label="À propos" />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button onClick={toggleMenu} className="text-gray-700 hover:text-blue-700 focus:outline-none">
              {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white">
            <MobileNavLink href="/" label="Accueil" onClick={toggleMenu} />
            <MobileNavLink href="/timer" label="Minuteur" onClick={toggleMenu} />
            <MobileNavLink href="/programs" label="Programmes" onClick={toggleMenu} />
            <MobileNavLink href="/progress" label="Progression" onClick={toggleMenu} />
            <MobileNavLink href="/features" label="Fonctionnalités" onClick={toggleMenu} />
            <MobileNavLink href="/about" label="À propos" onClick={toggleMenu} />
          </div>
        </div>
      )}
    </nav>
  )
}

function NavLink({ href, label }) {
  return (
    <Link href={href} className="text-gray-700 hover:text-blue-700 px-3 py-2 font-medium transition-colors">
      {label}
    </Link>
  )
}

function MobileNavLink({ href, label, onClick }) {
  return (
    <Link
      href={href}
      className="block text-gray-700 hover:text-blue-700 px-3 py-2 rounded-md text-base font-medium"
      onClick={onClick}
    >
      {label}
    </Link>
  )
}

